### 測試是否可以下載
POST https://localhost:44301/api/download/check
Content-Type: application/json

{
  "model_name": "G17",
  "device_id": "00001"
}

### 測試多個設備是否可以下載
POST  https://localhost:44301/api/download/check
Content-Type: application/json

{
  "model_name": "G17",
  "device_id": "00002"
}
### 測試多個設備是否可以下載
POST https://localhost:44301/api/download/check
Content-Type: application/json

{
  "model_name": "G17",
  "device_id": "00003"
}
### 測試多個設備是否可以下載
POST https://localhost:44301/api/download/check
Content-Type: application/json

{
  "model_name": "G17",
  "device_id": "00004"
}
### 測試多個設備是否可以下載
POST https://localhost:44301/api/download/check
Content-Type: application/json

{
  "model_name": "G17",
  "device_id": "00005"
}

### 測試多個設備是否可以下載
POST https://localhost:44301/api/download/check
Content-Type: application/json

{
  "model_name": "G17",
  "device_id": "00006"
}


### 通知下載完成
POST https://localhost:44301/api/download/complete
Content-Type: application/json

{
  "model_name": "G17",
  "device_id": "KAXUB12345"
}
