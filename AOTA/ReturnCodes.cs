﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;

namespace Android_OTA
{
    public class ReturnCodes
    {
        public enum WebApiReturnCodes : int
        {
            [Description("Save data successed")]
            SAVE_DATA_SUCCESSED = 0x01,
            [Description("IBT Activate successed")]
            IBT_ACTIVATE_SUCCESSED = 0x02,
            [Description("JWT login failed")]
            LOGIN_FAILED = 0x04,
            [Description("Hardware Hash available")]
            HARDWARE_HASH_AVAILABLE = 0x08,
            [Description("serial number available")]
            SERIAL_NUMBER_AVAILABLE = 0x10,
            [Description("the customer not exist")]
            CUSTOMER_ID_NOT_EXIST = 0x20,
            [Description("LICENSES EXCEEDED")]
            LICENSES_EXCEEDED = 0x40,
            [Description("IBT_LICENSES EXCEEDED")]
            IBT_LICENSES_EXCEEDED = 0x80,
            [Description("This hardware already activated")]
            ALREADY_ACTIVATED = 0x0100,
            [Description("This hardware already activated IBT")]
            ALREADY_ACTIVATED_IBT = 0x0200,
            [Description("input the invalid parameter")]
            INVALID_PARAMETER = 0x0400,
            [Description("The incorrect serial number")]
            INCORRECT_SERIAL_NUMBER = 0x0800,
            [Description("The incorrect ibt serial number")]
            INCORRECT_IBT_SERIAL_NUMBER = 0x1000,
            [Description("The incorrect serial number and ibt serial number")]
            INCORRECT_ANDROIDX86_AND_IBT_SERIAL_NUMBER = 0x2000,
            [Description("Update DB failed")]
            UPDATE_FAILED = 0x4000,
            [Description("The system not activated.")]
            SYSTEM_NOT_ACTIVATED = 0x8000

        }
    }

    public class ReturnCode
    {
        public int returnCode { get; set; }
        public string returnMsg { get; set; }
    }

}
