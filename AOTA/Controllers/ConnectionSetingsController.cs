﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Authorization;
using Android_OTA.Models;

namespace Android_OTA.Controllers
{
    [Authorize]
    public class ConnectionSettingsController : Controller
    {

        private readonly otaDbContext _context;

        public ConnectionSettingsController(otaDbContext context)
        {
            _context = context;
        }

        // GET: Users
        public async Task<IActionResult> Index()
        {
            return View(await _context.ConnectionSettings.ToListAsync());
        }

        // GET: Users/Details/5
        public async Task<IActionResult> Details(int idx)
        {
            if (idx <= 0)
            {
                return NotFound();
            }

            //if (!int.TryParse(idx, out int max))
            //    return StatusCode(500, "Invalid max_connections");

            var connectionSettings = await _context.ConnectionSettings
                .FirstOrDefaultAsync(m => m.idx == idx);
            if (connectionSettings == null)
            {
                return NotFound();
            }

            return View(connectionSettings);
        }

        // GET: Users/Create
        public IActionResult Create()
        {
            return View();
        }

        // POST: Users/Create
        // To protect from overposting attacks, enable the specific properties you want to bind to.
        // For more details, see http://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("setting_name,setting_value,description")] connection_settings connectionsSettings)
        {
            if (ModelState.IsValid)
            {
                _context.Add(connectionsSettings);
                await _context.SaveChangesAsync();
                return RedirectToAction(nameof(Index));
            }
            return View(connectionsSettings);
        }

        // GET: Users/Edit/5
        public async Task<IActionResult> Edit(int idx)
        {
            if (idx <= 0)
            {
                return NotFound();
            }

            var connectionSettings = await _context.ConnectionSettings.FindAsync(idx);
            if (connectionSettings == null)
            {
                return NotFound();
            }
            return View(connectionSettings);
        }

        // POST: Users/Edit/5
        // To protect from overposting attacks, enable the specific properties you want to bind to.
        // For more details, see http://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int idx, [Bind("idx,setting_name,setting_value,description")] connection_settings connectionSettings)
        {
            //if (idx != connectionSettings.idx)
            //{
            //    return NotFound();
            //}

            if (ModelState.IsValid)
            {
                try
                {
                    _context.Update(connectionSettings);
                    await _context.SaveChangesAsync();
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!ConnectionSettingsExists(connectionSettings.setting_name))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }
            return View(connectionSettings);
        }

        // GET: Users/Delete/5
        public async Task<IActionResult> Delete(int idx)
        {
            if (idx <= 0)
            {
                return NotFound();
            }

            var connectionSettings = await _context.ConnectionSettings
                .FirstOrDefaultAsync(m => m.idx == idx);
            if (connectionSettings == null)
            {
                return NotFound();
            }

            return View(connectionSettings);
        }

        // POST: Users/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int idx)
        {
            var connectionSettings = await _context.ConnectionSettings.FindAsync(idx);
            _context.ConnectionSettings.Remove(connectionSettings);
            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        private bool ConnectionSettingsExists(string settingName)
        {
            return _context.ConnectionSettings.Any(e => e.setting_name == settingName);
        }
    }
}
