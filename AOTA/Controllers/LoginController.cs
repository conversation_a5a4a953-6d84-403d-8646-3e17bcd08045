﻿using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Android_OTA.Models;
using Microsoft.AspNetCore.Http;
using System.Security.Claims;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authentication;
using Newtonsoft.Json.Linq;

namespace Android_OTA.Controllers
{
    public class LoginController : Controller
    {
        private readonly otaDbContext _context;

        public LoginController(otaDbContext context)
        {
            _context = context;
        }

        [HttpGet]
        public IActionResult Index()
        {

            if (User.Identity.IsAuthenticated) return Redirect("/Log");

            user _user = new user();
            return View(_user);
        }

        private bool LockAccount(string userId)
        {
            DateTime currentTime = DateTime.Now;
            DateTime fifteenMinutesAgo = currentTime.AddMinutes(-15);
            //where LoginFailed.UserId == userId && LoginFailed.FailedCount >5 && DateTime.Now - LoginFailed.FailedTime >  
            var result = (from lf in _context.LoginFailedSet
                        where lf.user_id == userId && lf.failed_count >= 5 && lf.failed_time > fifteenMinutesAgo
                        select lf).SingleOrDefault();
            return result != null;
        }

        private bool AddFailedLogin(string userId)
        {
            login_failed loginFailed = _context.LoginFailedSet.Find(userId);
            //var cust = (from customer in _androidx86_licenseContext.Customers
            //            where customer.CustomerId == value.CustomerId
            //            select customer).SingleOrDefault();
            if (loginFailed == null)
            {
                // insert DB
                loginFailed = new login_failed();
                loginFailed.user_id = userId;
                loginFailed.failed_count = 1;
                loginFailed.failed_time = DateTime.Now;
                _context.LoginFailedSet.Add(loginFailed);
            }
            else
            {
                // update DB
                loginFailed.failed_count++;
                loginFailed.failed_time = DateTime.Now;
            }
            _context.SaveChanges();
            return true;
        }

        private void DeleteFailedLogin(string userId)
        {
            var user = _context.LoginFailedSet.Find(userId);
            if (user != null)
            {
                _context.LoginFailedSet.Remove(user);
                _context.SaveChanges();
            }
        }

        [HttpPost]
        public IActionResult Index(user _user)
        {

            if (LockAccount(_user.user_id))
            {
                ViewBag.LoginStatus = -1;
                return View(_user);
            }
            var result = _context.Users.Where(a => a.user_id == _user.user_id && a.password == _user.password && a.type == "web").FirstOrDefault();
            if (result == null)
            {
                ViewBag.LoginStatus = 0;
                // process login failed 
                // count 5 times then lock this account for 15 mins
                AddFailedLogin(_user.user_id);
            }
            else
            {

                var claims = new List<Claim>
                {
                    new Claim(ClaimTypes.Name, result.user_id),
                    new Claim("UserName", result.user_name),
                    new Claim("UserID",result.user_id),
                    new Claim("UserType",result.type),
                   // new Claim(ClaimTypes.Role, "Administrator")
                };

                var claimsIdentity = new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme);

                //HttpContext.SignInAsync(CookieAuthenticationDefaults.AuthenticationScheme, new ClaimsPrincipal(claimsIdentity));

                HttpContext.SignInAsync(CookieAuthenticationDefaults.AuthenticationScheme, new ClaimsPrincipal(claimsIdentity),
                    new AuthenticationProperties
                    {
                        ExpiresUtc = DateTime.UtcNow.AddMinutes(15),
                        IsPersistent = true,
                        RedirectUri = "Users",
                        AllowRefresh = true
                    });

                DeleteFailedLogin(_user.user_id);
                HttpContext.Session.SetString("account", _user.user_id);
                return Redirect("/Log");

                //return RedirectToAction("LoginSuccessed", "Login");
            }
            return View(_user);
        }

        public IActionResult LoginSuccessed()
        {
            return View();
        }
    }
}
