﻿using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.IO;

namespace Android_OTA.Controllers
{
    public class GeoLite2Controller : Controller
    {
        private readonly IHostingEnvironment _hostEnvironment;

        public GeoLite2Controller(IHostingEnvironment environment)
        {
            _hostEnvironment = environment;
        }
       
        // GET: GeoLite2Controler        
        public ActionResult Index()
        {
            return View();
        }

        // GET: GeoLite2Controler/Details/5
        public ActionResult Details(int id)
        {
            return View();
        }

        // GET: GeoLite2Controler/Create
        public ActionResult Create()
        {
            return View();
        }

        [HttpPost]        
        public ActionResult UploadDb(IFormFile file)
        {
            if (file == null || file.Length == 0)
            {
                ViewData["ErrorMessage"] = "The uploaded file is empty.";
                return View();
            }
            string wwwRootPath = _hostEnvironment.WebRootPath;
            string fileName = Path.GetFileName(file.FileName);

            if (fileName != "GeoLite2-City.mmdb")
            {
                ViewData["ErrorMessage"] = "The uploaded file is incorrect.";
                return View();
            }
            try
            {
                // rename the file to GeoLite2-City.mmdb.bak
                //System.IO.File.Move(Path.Combine(wwwRootPath, "GeoLite2-City.mmdb"), Path.Combine(wwwRootPath, "GeoLite2-City.mmdb.bak"));
                if (System.IO.File.Exists(Path.Combine(wwwRootPath, fileName)))
                    System.IO.File.Move(Path.Combine(wwwRootPath, fileName), Path.Combine(wwwRootPath, fileName + ".bak"));

                // create the new GeoLite2-City.mmdb
                using (FileStream stream = new FileStream(Path.Combine(wwwRootPath, fileName), FileMode.Create))
                {
                    file.CopyTo(stream);
                    ViewData["Message"] = string.Format("{0} Upload finished", fileName);
                }
                // delete the bak file
                System.IO.File.Delete(Path.Combine(wwwRootPath, fileName + ".bak"));
            }
            catch (Exception ex)
            {
                // if the bak file exists, rename it back to GeoLite2-City.mmdb
                if (System.IO.File.Exists(Path.Combine(wwwRootPath, fileName + ".bak")))
                {
                    System.IO.File.Move(Path.Combine(wwwRootPath, fileName) + ".bak", Path.Combine(wwwRootPath, fileName));
                }
                ViewData["ErrorMessage"] = ex.Message;
                return View();
            }






            return View();
        }


        [HttpGet]
        public ActionResult UploadDb()
        {
            return View();
        }

        // POST: GeoLite2Controler/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create(IFormCollection collection)
        {
            try
            {
                return RedirectToAction(nameof(Index));
            }
            catch
            {
                return View();
            }
        }

        // GET: GeoLite2Controler/Edit/5
        public ActionResult Edit(int id)
        {
            return View();
        }

        // POST: GeoLite2Controler/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit(int id, IFormCollection collection)
        {
            try
            {
                return RedirectToAction(nameof(Index));
            }
            catch
            {
                return View();
            }
        }

        // GET: GeoLite2Controler/Delete/5
        public ActionResult Delete(int id)
        {
            return View();
        }

        // POST: GeoLite2Controler/Delete/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Delete(int id, IFormCollection collection)
        {
            try
            {
                return RedirectToAction(nameof(Index));
            }
            catch
            {
                return View();
            }
        }
    }
}
