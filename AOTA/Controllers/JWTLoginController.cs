﻿using Android_OTA;
using Android_OTA.Models;
using Android_OTA.RawData;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;

namespace Android_OTA.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class JWTLoginController : ControllerBase
    {
        private readonly otaDbContext _otaDbContext;
        private readonly IConfiguration _configuration;

        public JWTLoginController(otaDbContext dbContext, IConfiguration configuration)
        {
            _otaDbContext = dbContext;
            _configuration = configuration;
        }

        [HttpPost]
        public string Post(LoginPost value)
        {
            ReturnCode rc = new ReturnCode();
            var result = _otaDbContext.Users.Where(a => a.user_id == value.Account && a.password == value.Password && a.type == "web_api").FirstOrDefault();
            if (result == null)
            {
                return "login failed";
            }
            else
            {
                //
                var claims = new List<Claim>
                {
                    new Claim(JwtRegisteredClaimNames.Email, result.user_id),
                    new Claim("FullName", result.user_name),
                    new Claim(JwtRegisteredClaimNames.NameId, result.user_id)
                };

                //var role = from a in _todoContext.Roles
                //           where a.EmployeeId == user.EmployeeId
                //           select a;
                //foreach (var temp in role)
                //{
                //    claims.Add(new Claim(ClaimTypes.Role, temp.Name));
                //}

                //
                var securityKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_configuration["JWT:KEY"]));

                //
                var jwt = new JwtSecurityToken
                (
                    issuer: _configuration["JWT:Issuer"],
                    audience: _configuration["JWT:Audience"],
                    claims: claims,
                    expires: DateTime.Now.AddMinutes(5),
                    signingCredentials: new SigningCredentials(securityKey, SecurityAlgorithms.HmacSha256)
                );

                //
                var token = new JwtSecurityTokenHandler().WriteToken(jwt);

                //
                return token;
            }
        }

    }
}
