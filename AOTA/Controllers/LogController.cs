﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Authorization;
using Android_OTA.Models;

namespace Android_OTA.Controllers
{
    [Authorize]
    public class LogController : Controller
    {

        private readonly otaDbContext _context;

        public LogController(otaDbContext context)
        {
            _context = context;
        }

        public async Task<IActionResult> Index(string searchString, string sortOrder, int pageNumber, string currentFilter)
        {


            ViewBag.CurrentSort = sortOrder;
            ViewBag.DateSortParm = String.IsNullOrEmpty(sortOrder) || sortOrder == "date_desc" ? "date_asc" : "date_desc";
            ViewBag.DeviceSortParm = sortOrder == "device_asc" ? "device_desc" : "device_asc";
            ViewBag.CurrentFilter = searchString;


            ViewData["CurrentSort"] = sortOrder;
            //Sorting
            ViewData["date_desc"] = string.IsNullOrEmpty(sortOrder) ? "date_desc" : "";
            if (searchString != null)
            {
                pageNumber = 1;
            }
            else
            {
                searchString = currentFilter;
            }

            ViewData["CurrentFilter"] = searchString;

            var logsQuery = _context.Logs.AsQueryable();

            if (!string.IsNullOrEmpty(searchString))
            {
                logsQuery = logsQuery.Where(e => e.device_id.Contains(searchString));
            }

            logsQuery = sortOrder switch
            {
                "date_asc" => logsQuery.OrderBy(l => l.date_time),
                "device_asc" => logsQuery.OrderBy(l => l.device_id),
                "device_desc" => logsQuery.OrderByDescending(l => l.device_id),
                _ => logsQuery.OrderByDescending(l => l.date_time), // 預設最新在前
            };

            var logs = await logsQuery.ToListAsync();

            // Ensure pageNumber is at least 1
            if (pageNumber < 1)
            {
                pageNumber = 1;
            }

            int pageSize = 20;
            return View(await PaginatedList<log>.CreateAsync(logsQuery, pageNumber, pageSize));

        }

        public async Task<IActionResult> Delete(int idx)
        {
            if (idx <= 0)
            {
                return NotFound();
            }

            var completedConnections = await _context.Logs
                .FirstOrDefaultAsync(m => m.idx == idx);
            if (completedConnections == null)
            {
                return NotFound();
            }

            return View(completedConnections);
        }

        // POST: Users/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int idx)
        {
            var log = await _context.Logs.FindAsync(idx);
            _context.Logs.Remove(log);
            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }
    }
}
