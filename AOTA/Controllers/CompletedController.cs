﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Authorization;
using Android_OTA.Models;

namespace Android_OTA.Controllers
{
    [Authorize]
    public class CompletedController : Controller
    {

        private readonly otaDbContext _context;

        public CompletedController(otaDbContext context)
        {
            _context = context;
        }

        //// GET: Users
        //public async Task<IActionResult> Index()
        //{
        //    return View(await _context.CompletedConnections.ToListAsync());
        //}


        //public async Task<IActionResult> Index(string searchString, string sortOrder, int pageNumber, string currentFilter)
        //{
        //    ViewData["CurrentSort"] = sortOrder;
        //    //Sorting
        //    ViewData["finish_date_desc"] = string.IsNullOrEmpty(sortOrder) ? "finish_date_desc" : "";
        //    if (searchString != null)
        //    {
        //        pageNumber = 1;
        //    }
        //    else
        //    {
        //        searchString = currentFilter;
        //    }

        //    ViewData["CurrentFilter"] = searchString;

        //    var CompletedQuery = _context.CompletedConnections.AsQueryable();

        //    if (!string.IsNullOrEmpty(searchString))
        //    {
        //        CompletedQuery = CompletedQuery.Where(e => e.device_id.Contains(searchString));
        //    }

        //    CompletedQuery = sortOrder switch
        //    {
        //        "finish_date_desc" => CompletedQuery.OrderByDescending(e => e.finish_date),
        //        "finish_date_asc" => CompletedQuery.OrderBy(e => e.finish_date),
        //        _ => CompletedQuery.OrderByDescending(e => e.finish_date) // 預設排序
        //    };

        //    var logs = await CompletedQuery.ToListAsync();

        //    // Ensure pageNumber is at least 1
        //    if (pageNumber < 1)
        //    {
        //        pageNumber = 1;
        //    }

        //    int pageSize = 10;
        //    return View(await PaginatedList<completed_connections>.CreateAsync(CompletedQuery, pageNumber, pageSize));

        //}


        // GET: Users/Details/5
        //public async Task<IActionResult> Details(int idx)
        //{
        //    if (idx <= 0)
        //    {
        //        return NotFound();
        //    }

        //    //if (!int.TryParse(idx, out int max))
        //    //    return StatusCode(500, "Invalid max_connections");

        //    var connectionSettings = await _context.ConnectionSettings
        //        .FirstOrDefaultAsync(m => m.idx == idx);
        //    if (connectionSettings == null)
        //    {
        //        return NotFound();
        //    }

        //    return View(connectionSettings);
        //}

        // GET: Users/Create
        //public IActionResult Create()
        //{
        //    return View();
        //}

        // POST: Users/Create
        // To protect from overposting attacks, enable the specific properties you want to bind to.
        // For more details, see http://go.microsoft.com/fwlink/?LinkId=317598.
        //[HttpPost]
        //[ValidateAntiForgeryToken]
        //public async Task<IActionResult> Create([Bind("setting_name,setting_value,description")] connection_settings connectionsSettings)
        //{
        //    if (ModelState.IsValid)
        //    {
        //        _context.Add(connectionsSettings);
        //        await _context.SaveChangesAsync();
        //        return RedirectToAction(nameof(Index));
        //    }
        //    return View(connectionsSettings);
        //}

        // GET: Users/Edit/5
        //public async Task<IActionResult> Edit(int idx)
        //{
        //    if (idx <= 0)
        //    {
        //        return NotFound();
        //    }

        //    var connectionSettings = await _context.ConnectionSettings.FindAsync(idx);
        //    if (connectionSettings == null)
        //    {
        //        return NotFound();
        //    }
        //    return View(connectionSettings);
        //}

        // POST: Users/Edit/5
        // To protect from overposting attacks, enable the specific properties you want to bind to.
        // For more details, see http://go.microsoft.com/fwlink/?LinkId=317598.
        //[HttpPost]
        //[ValidateAntiForgeryToken]
        //public async Task<IActionResult> Edit(int idx, [Bind("idx,setting_name,setting_value,description")] connection_settings connectionSettings)
        //{
        //    //if (idx != connectionSettings.idx)
        //    //{
        //    //    return NotFound();
        //    //}

        //    if (ModelState.IsValid)
        //    {
        //        try
        //        {
        //            _context.Update(connectionSettings);
        //            await _context.SaveChangesAsync();
        //        }
        //        catch (DbUpdateConcurrencyException)
        //        {
        //            if (!ConnectionSettingsExists(connectionSettings.setting_name))
        //            {
        //                return NotFound();
        //            }
        //            else
        //            {
        //                throw;
        //            }
        //        }
        //        return RedirectToAction(nameof(Index));
        //    }
        //    return View(connectionSettings);
        //}

        public async Task<IActionResult> Index(string searchString, string sortOrder, int pageNumber, string currentFilter)
        {


            ViewBag.CurrentSort = sortOrder;
            ViewBag.DateSortParm = String.IsNullOrEmpty(sortOrder) || sortOrder == "date_desc" ? "date_asc" : "date_desc";
            ViewBag.DeviceSortParm = sortOrder == "device_asc" ? "device_desc" : "device_asc";
            ViewBag.ElapsedSortParm = sortOrder == "elapsed_asc" ? "elapsed_desc" : "elapsed_asc";
            ViewBag.CurrentFilter = searchString;


            ViewData["CurrentSort"] = sortOrder;
            //Sorting
            ViewData["date_desc"] = string.IsNullOrEmpty(sortOrder) ? "date_desc" : "";
            if (searchString != null)
            {
                pageNumber = 1;
            }
            else
            {
                searchString = currentFilter;
            }

            ViewData["CurrentFilter"] = searchString;

            var completedQuery = _context.CompletedConnections.AsQueryable();

            if (!string.IsNullOrEmpty(searchString))
            {
                completedQuery = completedQuery.Where(e => e.device_id.Contains(searchString));
            }

            completedQuery = sortOrder switch
            {
                "date_asc" => completedQuery.OrderBy(l => l.finish_date),
                "device_asc" => completedQuery.OrderBy(l => l.device_id),
                "device_desc" => completedQuery.OrderByDescending(l => l.device_id),
                "elapsed_asc" => completedQuery.OrderBy(l => l.elapsed_time),
                "elapsed_desc" => completedQuery.OrderByDescending(l => l.elapsed_time),
                _ => completedQuery.OrderByDescending(l => l.finish_date), // 預設最新在前
            };

            var completed = await completedQuery.ToListAsync();

            // Ensure pageNumber is at least 1
            if (pageNumber < 1)
            {
                pageNumber = 1;
            }

            int pageSize = 20;
            return View(await PaginatedList<completed_connections>.CreateAsync(completedQuery, pageNumber, pageSize));

        }


        public async Task<IActionResult> Delete(int idx)
        {
            if (idx <= 0)
            {
                return NotFound();
            }

            var completedConnections = await _context.CompletedConnections
                .FirstOrDefaultAsync(m => m.idx == idx);
            if (completedConnections == null)
            {
                return NotFound();
            }

            return View(completedConnections);
        }

        // POST: Users/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int idx)
        {
            var completed = await _context.CompletedConnections.FindAsync(idx);
            _context.CompletedConnections.Remove(completed);
            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        private bool ConnectionSettingsExists(string settingName)
        {
            return _context.ConnectionSettings.Any(e => e.setting_name == settingName);
        }
    }
}
