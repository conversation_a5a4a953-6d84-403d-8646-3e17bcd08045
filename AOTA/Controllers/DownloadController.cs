﻿using Microsoft.AspNetCore.Mvc;

using Android_OTA.Models;
using Android_OTA.RawData;
using Microsoft.EntityFrameworkCore;
using System.Threading.Tasks;
using System.Linq;
using System;

using Pomelo.EntityFrameworkCore.MySql;

namespace Android_OTA.Controllers
{

    [Route("api/[controller]")]
    [ApiController]
    public class DownloadController : Controller
    {
        private readonly otaDbContext _otaDbContext;

        public DownloadController(otaDbContext dbContext)
        {
            _otaDbContext = dbContext;
        }

        [HttpPost("check")]
        public async Task<IActionResult> CheckDownloadPermission([FromBody] DevicePostData request)
        {
            var now = DateTime.Now;

            // 取得 timeout_duration 秒數
            var timeoutStr = await _otaDbContext.ConnectionSettings
                .Where(x => x.setting_name == "timeout_duration")
                .Select(x => x.setting_value)
                .FirstOrDefaultAsync();

            if (!int.TryParse(timeoutStr, out int timeout))
                return StatusCode(500, "Invalid timeout_duration");

            // 刪除 timeout 的連線
            var expired = _otaDbContext.CurrentConnections
                .Where(x => Microsoft.EntityFrameworkCore.MySqlDbFunctionsExtensions.DateDiffSecond(EF.Functions, x.start_date, now) > timeout); // Specify MySqlDbFunctionsExtensions explicitly
            _otaDbContext.CurrentConnections.RemoveRange(expired);
            await _otaDbContext.SaveChangesAsync();

            // 取得 max_connections 數
            var maxStr = await _otaDbContext.ConnectionSettings
                .Where(x => x.setting_name == "max_connections")
                .Select(x => x.setting_value)
                .FirstOrDefaultAsync();

            if (!int.TryParse(maxStr, out int max))
                return StatusCode(500, "Invalid max_connections");

            // 取得 wait_time 數
            var waitStr = await _otaDbContext.ConnectionSettings
                .Where(x => x.setting_name == "wait_time")
                .Select(x => x.setting_value)
                .FirstOrDefaultAsync();

            if (!int.TryParse(waitStr, out int wait))
                return StatusCode(500, "Invalid wait_time");

            var currentCount = await _otaDbContext.CurrentConnections.CountAsync();

            if (currentCount >= max)
            {
                await Log(request.device_id, "Rejected", 503, $"Max connections reached,Please wait for {wait} seconds");
                return StatusCode(503, "Maximum concurrent connections reached.");
            }

            // Check if device_id already exists
            var existing = await _otaDbContext.CurrentConnections
                .FirstOrDefaultAsync(x => x.device_id == request.device_id);

            if (existing != null)
            {
                existing.model_name = request.model_name;
                existing.start_date = now;
                _otaDbContext.CurrentConnections.Update(existing);
            }
            else
            {
                _otaDbContext.CurrentConnections.Add(new current_connections
                {
                    model_name = request.model_name,
                    device_id = request.device_id,
                    start_date = now
                });
            }

            await _otaDbContext.SaveChangesAsync();
            await Log(request.device_id, "Allowed", 200, "Download permitted.");

            return Ok("Download permitted.");
        }

        [HttpPost("complete")]
        public async Task<IActionResult> CompleteDownload([FromBody] DevicePostData request)
        {
            var now = DateTime.UtcNow;
            var conn = await _otaDbContext.CurrentConnections
                .Where(x => x.device_id == request.device_id && x.model_name == request.model_name)
                .FirstOrDefaultAsync();

            if (conn == null)
            {
                await Log(request.device_id, "Not Found", 404, "No active connection found.");
                return NotFound("No active connection found.");
            }

            var elapsed = (int)(now - conn.start_date).TotalSeconds;

            // 移除 current_connections 並加入 completed_connections
            _otaDbContext.CurrentConnections.Remove(conn);
            _otaDbContext.CompletedConnections.Add(new completed_connections
            {
                model_name = conn.model_name,
                device_id = conn.device_id,
                start_date = conn.start_date,
                finish_date = now,
                elapsed_time = elapsed
            });

            await _otaDbContext.SaveChangesAsync();
            await Log(request.device_id, "Completed", 200, "Download completed.");

            return Ok("Download completed.");
        }

        private async Task Log(string deviceId, string status, int code, string message)
        {
            var ip = HttpContext.Connection.RemoteIpAddress?.ToString();
            _otaDbContext.Logs.Add(new log
            {
                device_id = deviceId,
                download_status = status,
                status = $"Code: {code}, Message: {message}",
                date_time = DateTime.Now,
                ip = ip
            });

            await _otaDbContext.SaveChangesAsync();
        }


    }
}
