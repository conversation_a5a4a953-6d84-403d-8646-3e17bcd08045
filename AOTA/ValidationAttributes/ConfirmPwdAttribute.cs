﻿using System.ComponentModel.DataAnnotations;
using System;
using System.Text.RegularExpressions;

namespace Android_OTA.ValidationAttributes
{
    //[AttributeUsage(AttributeTargets.Property | AttributeTargets.Field | AttributeTargets.Parameter)]
    public class ConfirmPwdAttribute : ValidationAttribute
    {
        private readonly string _targetName;

        public ConfirmPwdAttribute(string targetFieldName)
        {
            this._targetName = targetFieldName;
        }

        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            this.ErrorMessage = this.ErrorMessageString;
            if (value == null) { return new ValidationResult($"{validationContext.MemberName} is null"); }
            var sourceType = value.GetType();
            var sourceName = validationContext.MemberName;

            if (sourceType == typeof(IComparable))
            {
                throw new ArgumentException("value has not implemented IComparable interface");
            }

            var sourceValue = (IComparable)value;
            var comparisonPropertyInfo = validationContext.ObjectType.GetProperty(this._targetName);
            if (comparisonPropertyInfo == null)
            {
                throw new ArgumentException("Comparison property with this name not found");
            }

            var targetValue = comparisonPropertyInfo.GetValue(validationContext.ObjectInstance);
            if (targetValue == null) return ValidationResult.Success;
            var targetType = targetValue.GetType();
            
            if (targetType == typeof(IComparable))
            {
                throw new ArgumentException("Comparison property has not implemented IComparable interface");
            }

            if (!ReferenceEquals(sourceType, targetType))
            {
                throw new ArgumentException("The properties types must be the same");
            }

            if (sourceValue.ToString() != targetValue.ToString())
            {
                this.ErrorMessage = $"Password is no match";
                return new ValidationResult(this.ErrorMessage);
            }

            return ValidationResult.Success;

        }
    }
}
