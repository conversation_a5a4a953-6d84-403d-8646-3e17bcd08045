﻿using System.ComponentModel.DataAnnotations;
using System;
using System.Text.RegularExpressions;

namespace Android_OTA.ValidationAttributes
{
    //[AttributeUsage(AttributeTargets.Property | AttributeTargets.Field | AttributeTargets.Parameter)]
    public class IdAttribute : ValidationAttribute
    {
        string[] blocIdList = new string[] {"users","edit","delete","details","exists"};
        public IdAttribute()
        {
            
        }

        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            this.ErrorMessage = this.ErrorMessageString;
            var sourceType = value.GetType();
            var sourceName = validationContext.MemberName;

            if (sourceType == typeof(IComparable))
            {
                throw new ArgumentException("value has not implemented IComparable interface");
            }

            var sourceValue = (IComparable)value;

            foreach(string blockId in blocIdList)
            {
                if (string.IsNullOrEmpty(blockId)) { continue; }
                if(sourceValue.ToString().ToLower() == blockId)
                {
                    return new ValidationResult($"Invalid {sourceName}");
                }
            }

            //string regexPattern = "^[a-zA-Z][a-zA-Z0-9]{1,49}\\.*$";
            string regexPattern = "^[a-zA-Z][a-zA-Z0-9\\.]{1,49}$";
            //string regexPattern = "^[a-zA-Z][a-zA-Z0-9]*\\.[a-zA-Z0-9]*$";


            bool isMatch = Regex.IsMatch(sourceValue.ToString(), regexPattern);

            if (isMatch)
            {                
                // Perform further operations with the valid UserId
                return ValidationResult.Success;
            }
            else
            {
                this.ErrorMessage = $"{sourceName} is not valid. 1. Start with a-z or A-Z, at least one\r\n\r\n2. Must include a-z, A-Z, 0-9, at least one\r\n\r\n3. Accept \".\", at least one\r\n\r\n4. Minimum 2 digits and maximum 50 digits\r\n\r\n5. User ID should be unique\r\n\r\n6. User ID should not be start with number";
                return new ValidationResult(this.ErrorMessage);
                // Handle the case when UserId is not valid
            }
            
        }
    }
}
