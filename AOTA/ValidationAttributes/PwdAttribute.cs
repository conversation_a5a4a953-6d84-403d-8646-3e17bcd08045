﻿using System.ComponentModel.DataAnnotations;
using System;
using System.Text.RegularExpressions;

namespace Android_OTA.ValidationAttributes
{
    [AttributeUsage(AttributeTargets.Property | AttributeTargets.Field | AttributeTargets.Parameter)]
    public class PwdAttribute : ValidationAttribute
    {

        public PwdAttribute()
        {

        }

        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            this.ErrorMessage = this.ErrorMessageString;
            if (value == null) { return new ValidationResult($"{validationContext.MemberName} is null"); }
            var sourceType = value.GetType();
            var sourceName = validationContext.MemberName;

            if (sourceType == typeof(IComparable))
            {
                throw new ArgumentException("value has not implemented IComparable interface");
            }

            var sourceValue = (IComparable)value;

            string regexPattern = "^(?=.*[A-Z].*[A-Z])(?=.*[0-9])(?=\\S+$).{8,48}$";


            bool isMatch = Regex.IsMatch(sourceValue.ToString(), regexPattern);

            if (isMatch)
            {
                // Perform further operations with the valid UserId
                return ValidationResult.Success;
            }
            else
            {
                this.ErrorMessage = $"{sourceName} is not valid. 1. Set Password min to 8 digits and max to 48 digits \r\n2. Set Password include 2 upper case letter and 1 number digit \r\n3. not allowed use space ";
                return new ValidationResult(this.ErrorMessage);
                // Handle the case when UserId is not valid
            }

        }
    }
}
