﻿using System.ComponentModel.DataAnnotations;
using System;
using System.Text.RegularExpressions;

namespace Android_OTA.ValidationAttributes
{
    //[AttributeUsage(AttributeTargets.Property | AttributeTargets.Field | AttributeTargets.Parameter)]
    public class UserTypeAttribute : ValidationAttribute
    {

        public UserTypeAttribute()
        {

        }

        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            this.ErrorMessage = this.ErrorMessageString;
            if (value == null || value.ToString() == String.Empty)
            {
                //if (propertyValue == null || propertyValue.ToString() == String.Empty)
                //{
                return new ValidationResult("IsValid object value is null");
                //}
            }

            var sourceType = value.GetType();
            var sourceName = validationContext.MemberName;


            if (sourceType == typeof(IComparable))
            {
                throw new ArgumentException("value has not implemented IComparable interface");
            }

            var sourceValue = (IComparable)value;

            

            if (sourceValue.ToString() != "web" && sourceValue.ToString() != "web_api")
            {
                this.ErrorMessage = $"{sourceName} Please select the type";
                return new ValidationResult(this.ErrorMessage);
                // Perform further operations with the valid UserId
            }
            else
            {
                return ValidationResult.Success;
                // Handle the case when UserId is not valid
            }

        }
    }
}
