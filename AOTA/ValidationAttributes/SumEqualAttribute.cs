﻿using System.ComponentModel.DataAnnotations;
using System;
using Microsoft.AspNetCore.Mvc.Filters;

namespace Android_OTA.ValidationAttributes
{
    [AttributeUsage(AttributeTargets.Property | AttributeTargets.Field | AttributeTargets.Parameter)]
    public class SumEqualAttribute : ValidationAttribute
    {
        private readonly string _targetName1;
        private readonly string _targetName2;


        public SumEqualAttribute(string targetFieldName1,string targetFieldName2)
        {
            this._targetName1 = targetFieldName1;
            this._targetName2 = targetFieldName2;
        }

        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            this.ErrorMessage = this.ErrorMessageString;
            var sourceType = value.GetType();
            var sourceName = validationContext.MemberName;

            if (sourceType == typeof(IComparable))
            {
                throw new ArgumentException("value has not implemented IComparable interface");
            }

            var sourceValue = (IComparable)value;
            var comparisonPropertyInfo1 = validationContext.ObjectType.GetProperty(this._targetName1);
            var comparisonPropertyInfo2 = validationContext.ObjectType.GetProperty(this._targetName2);
            if (comparisonPropertyInfo1 == null)
            {
                throw new ArgumentException("Comparison1 property with this name not found");
            }
            if (comparisonPropertyInfo2 == null)
            {
                throw new ArgumentException("Comparison2 property with this name not found");
            }
            var targetValue1 = comparisonPropertyInfo1.GetValue(validationContext.ObjectInstance);
            var targetValue2 = comparisonPropertyInfo2.GetValue(validationContext.ObjectInstance);
            var targetType1 = targetValue1.GetType();
            var targetType2 = targetValue2.GetType();
            if (targetType1 == typeof(IComparable))
            {
                throw new ArgumentException("Comparison property1 has not implemented IComparable interface");
            }

            if (!ReferenceEquals(sourceType, targetType1))
            {
                throw new ArgumentException("The properties types must be the same");
            }

            if (targetType2 == typeof(IComparable))
            {
                throw new ArgumentException("Comparison property2 has not implemented IComparable interface");
            }

            if (!ReferenceEquals(sourceType, targetType2))
            {
                throw new ArgumentException("The properties types must be the same");
            }

            // 

            if((int)sourceValue!=((int)targetValue1+(int)targetValue2))
            {
                this.ErrorMessage = $"{sourceName} should set to be equal to {_targetName1} + {_targetName2}";
                return new ValidationResult(this.ErrorMessage);
            }

            return ValidationResult.Success;
        }
    }
}
