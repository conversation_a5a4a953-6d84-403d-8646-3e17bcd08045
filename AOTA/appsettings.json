{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "AllowedHosts": "*",
  "ConnectionStrings": {
    //"AndroidX86LicenseDB": "Server=127.0.0.1;Database=AndroidX86_License;Trusted_Connection=True;User ID=sa;Password=********",
    //"ftbdcDb_SqlServer": "Server=127.0.0.1;Database=ftbdcDb;Trusted_Connection=false;User ID=sa;Password=********",
    "otaDb_MySql": "Server=ftbdcdb.mysql.database.azure.com;Database=aota;User ID=ftswadmin;Password=*************************",
    //"ftbdcDb_MySql": "Server=ftbdcdb.mysql.database.azure.com;Database=ftbdcDb;User ID=ftswadmin;Password=*************************"
    //"ftbdcDb_MySql": "Server=localhost;Database=ftbdcDb;User ID=root;Password=*************************"
    //"AndroidX86LicenseDB": "Server=flyprdsqlsrv001.database.windows.net;Database=LicenseMgr;Trusted_Connection=False;Encrypt=True;;User ID=ftautologon;Password=Wp$Pu-ZYZK#U8nMBDyjN#9gC"
    //"AndroidX86LicenseDB": "Server=flyprdsqlsrv001.database.windows.net;Database=LicenseMgr;Trusted_Connection=False;Encrypt=True;;User ID=ftautologon;Password=Wp$Pu-ZYZK#U8nMBDyjN#9gC"
  },
  "AzureGeolocationUri": "https://atlas.microsoft.com/geolocation/ip/json?api-version=1.0",
  "AzureSubscriptionKeyPrimary": "XEpvu5EWgKiJi9Nm4iFv7bMnW3VwG_9ehNLrkjA5MDQ",
  "AzureSubscriptionKeySecondary": "sFzUgYGIAJmfi0EIRh57rF7J9bA-nmQbEBAfJmpzKDQ",
  "JWT": {
    "KEY": "BigDataCollection:www.flytech.com",
    "Issuer": "flytech.com",
    "Audience": "my"
  }
}
