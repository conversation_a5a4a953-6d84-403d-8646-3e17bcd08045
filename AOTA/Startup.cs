﻿using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.HttpsPolicy;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Http;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using Android_OTA.Models;
using Pomelo.EntityFrameworkCore.MySql.Infrastructure;
//using Android_OTA.Repositories;

namespace Android_OTA
{
    public class Startup
    {
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        public IConfiguration Configuration { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            services.AddControllersWithViews();
            services.AddSession();
            string connectionString = Configuration.GetConnectionString("otaDb_MySql");

            //services.AddDbContext<otaDbContext>(options =>
            //options.UseSqlServer(Configuration.GetConnectionString("ftbdcDb_SqlServer")));
            services.AddDbContext<otaDbContext>(options =>
            {
                options.UseMySql(connectionString, ServerVersion.AutoDetect(connectionString));
            });

            //services.AddScoped<IDeviceRepository, DeviceRepository>();

            services.AddHttpContextAccessor();
            //services.AddAuthentication(CookieAuthenticationDefaults.AuthenticationScheme).AddCookie(option =>
            //{
            //    //未登入時會自動導到這個網址
            //    option.LoginPath = new PathString("/login");
            //});

            //services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
            //    .AddJwtBearer(options =>
            //    {
            //        options.TokenValidationParameters = new TokenValidationParameters
            //        {
            //            ValidateIssuer = true,
            //            ValidIssuer = Configuration["Jwt:Issuer"],
            //            ValidateAudience = true,
            //            ValidAudience = Configuration["Jwt:Audience"],
            //            ValidateLifetime = true,
            //            IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(Configuration["JWT:KEY"]))
            //        };
            //    });


            services.AddAuthentication(options =>
            {
                options.DefaultScheme = CookieAuthenticationDefaults.AuthenticationScheme;
            })
            .AddCookie(options =>
             {
                 options.LoginPath = "/Login";
                 options.AccessDeniedPath = "/Auth/Denied";
                 options.Events.OnRedirectToLogin = context =>
                 {
                     if (context.Request.Path.StartsWithSegments("/api"))
                     {
                         context.Response.Clear();
                         context.Response.StatusCode = StatusCodes.Status401Unauthorized;
                         return Task.CompletedTask;
                     }
                     context.Response.Redirect(context.RedirectUri);
                     return Task.CompletedTask;
                 };
             })
             .AddJwtBearer(JwtBearerDefaults.AuthenticationScheme, jwtBearerOptions =>
             {
                 jwtBearerOptions.TokenValidationParameters = new TokenValidationParameters
                 {
                     ValidateIssuerSigningKey = true,
                     IssuerSigningKey = new SymmetricSecurityKey(Encoding.ASCII.GetBytes(Configuration["Jwt:Key"])),

                     ValidateIssuer = true,
                     ValidIssuer = Configuration["Jwt:Issuer"],

                     ValidateAudience = true,
                     ValidAudience = Configuration["Jwt:Audience"],


                     ValidateLifetime = true,
                     ClockSkew = TimeSpan.FromMinutes(5)
                 };
             });
            //            //Jwt Authentication
            //            services.AddAuthentication(opts =>
            //            {
            //                //opts.DefaultAuthenticateScheme = CookieAuthenticationDefaults.AuthenticationScheme;
            //                //opts.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
            //            })
            //              //
            //              .AddPolicyScheme(settings.App, "Bearer or Jwt", options =>
            //              {
            //                  options.ForwardDefaultSelector = context =>
            //                  {
            //                      var bearerAuth = context.Request.Headers["Authorization"].FirstOrDefault()?.StartsWith("Bearer ") ?? false;
            //                // You could also check for the actual path here if that's your requirement:
            //                // eg: if (context.HttpContext.Request.Path.StartsWithSegments("/api", StringComparison.InvariantCulture))
            //                if (bearerAuth)
            //                          return JwtBearerDefaults.AuthenticationScheme;
            //                      else
            //                          return CookieAuthenticationDefaults.AuthenticationScheme;
            //                  };
            //              })
            //       .AddCookie(CookieAuthenticationDefaults.AuthenticationScheme, options =>
            //       {
            //                options.LoginPath = "/Identity/Account/Login";
            //                options.LogoutPath = "/Identity/Account/Logout";
            //                options.AccessDeniedPath = "/Identity/Account/AccessDenied";
            //                options.Cookie.Name = "CustomerPortal.Identity";
            //                options.SlidingExpiration = true;
            //                options.ExpireTimeSpan = TimeSpan.FromSeconds(10); //Account.Login overrides this default value
            //            })
            //        .AddJwtBearer(x =>
            //         {
            //             x.RequireHttpsMetadata = false;
            //             x.SaveToken = true;
            //             x.TokenValidationParameters = new TokenValidationParameters
            //             {
            //                 ValidateIssuerSigningKey = true,
            //                 IssuerSigningKey = new SymmetricSecurityKey(Encoding.ASCII.GetBytes(Configuration["Jwt:Key"])),
            //                 ValidateIssuer = true,
            //                 ValidateAudience = true,
            //                 ValidateLifetime = true,
            //                 ValidIssuer = Configuration["Jwt:Issuer"],
            //                 ValidAudience = Configuration["Jwt:Issuer"],
            //             };
            //         });

            //            services.ConfigureApplicationCookie(options =>
            //            {
            //                // Cookie settings
            //                options.Cookie.Name = settings.App;
            //                options.Cookie.HttpOnly = true;
            //                options.ExpireTimeSpan = TimeSpan.FromSeconds(10);
            //                options.LoginPath = "/Identity/Account/Login";
            //                options.LogoutPath = "/Identity/Account/Logout";
            //                options.Events = new CookieAuthenticationEvents()
            //                {
            //                    OnRedirectToLogin = context =>
            //                    {
            //                        if (context.Request.Path.Value.StartsWith("/api"))
            //                        {
            //                            context.Response.Clear();
            //                            context.Response.StatusCode = 401;
            //                            return Task.FromResult(0);
            //                        }
            //                        context.Response.Redirect(context.RedirectUri);
            //                        return Task.FromResult(0);
            //                    }
            //                };
            //                //options.AccessDeniedPath = "/Identity/Account/AccessDenied";
            //            });


        }



        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }
            else
            {
                app.UseExceptionHandler("/Home/Error");
                // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
                app.UseHsts();
            }
            app.UseSession();
            app.UseHttpsRedirection();
            app.UseStaticFiles();
            app.UseRouting();
            app.UseCookiePolicy();
            app.UseAuthentication();
            app.UseAuthorization();

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllerRoute(
                    name: "default",
                    pattern: "{controller=login}/{action=Index}/{id?}");
            });
        }
    }
}
