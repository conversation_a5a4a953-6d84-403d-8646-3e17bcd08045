﻿@model Android_OTA.Models.log
@using Microsoft.AspNetCore.Http


@{
    ViewData["Title"] = "Delete";
}

<h1>Delete</h1>

<h3>Are you sure you want to delete this?</h3>
<div>
    <h4>User</h4>
    <hr />
    <dl class="row">
        <dt class="col-sm-2">
            @Html.DisplayNameFor(model => model.device_id)
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.device_id)
        </dd>
        <dt class="col-sm-2">
            @Html.DisplayNameFor(model => model.download_status)
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.download_status)
        </dd>
        <dt class="col-sm-2">
            @Html.DisplayNameFor(model => model.date_time)
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.date_time)
        </dd>
        <dt class="col-sm-2">
            @Html.DisplayNameFor(model => model.ip)
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.ip)
        </dd>
        <dt class="col-sm-2">
            @Html.DisplayNameFor(model => model.status)
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.status)
        </dd>

    </dl>

    <form asp-action="Delete">
        <input type="hidden" asp-for="idx" />
        <input type="submit" value="Delete" class="btn btn-danger" /> |
        <a asp-action="Index">Back to List</a>
    </form>
</div>
