﻿@* @model IEnumerable<Android_OTA.Models.log> *@
@using Microsoft.AspNetCore.Http
@model PaginatedList<Android_OTA.Models.log>

@{
    ViewData["Title"] = "Index";
    var currentFilter = ViewBag.CurrentFilter as string ?? "";
    var currentSort = ViewBag.CurrentSort as string ?? "";
}

<h1>Android OTA Logs</h1>
@* 
<p>
    <a asp-action="Create">Create New</a>
</p>
 *@


<form asp-action="Index" method="get" class="row gy-2 gx-3 align-items-center mb-3">
    <div class="col-auto">
    </div>
    <div class="col-auto">
        <label for="searchString" class="col-form-label">Device ID:</label>
    </div>
    <div class="col-auto">
        <input type="text"
               name="searchString"
               id="searchString"
               value="@ViewBag.CurrentFilter"
               class="form-control"
               placeholder="Search by Device ID" />
    </div>
    <div class="col-auto">
        <button type="submit" class="btn btn-primary">Search</button>
    </div>
</form>

<table class="table">
    <thead>
        <tr>
            <th>
                @Html.ActionLink(
                         "Date Time",
                         "Index",
                         new { sortOrder = ViewBag.DateSortParm, searchString = currentFilter }
                         )
            </th>
            <th>
                @Html.ActionLink(
                         "Device ID",
                         "Index",
                         new { sortOrder = ViewBag.DeviceSortParm, searchString = currentFilter }
                         )
            </th>
            <th scope="col">download_status</th>
            <th scope="col">ip</th>
            <th scope="col">status</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model.Items)
        {
            <tr>
                <td>
                    @item.date_time.ToString("yyyy/MM/dd HH:mm:ss")
                </td>

                <td>
                    @Html.DisplayFor(modelItem => item.device_id)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.download_status)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.ip)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.status)
                </td>
                <td>
@*                     <a asp-action="Edit" asp-route-idx="@item.idx">Edit</a> |
                    <a asp-action="Details" asp-route-idx="@item.idx">Details</a> |
                    <a asp-action="Delete" asp-route-idx="@item.idx">Delete</a>
 *@                 <a asp-action="Delete" asp-route-idx="@item.idx">Delete</a>
                </td>
            </tr>
        }
        @*Paging*@
        <div class="row">
            <div class="col-md-6">
            </div>
            <div class="col-md-6">
                <nav>
                    <ul class="pagination justify-content-end">
                        <li class="page-item @(Model.PageIndex == 1 ? "disabled" : "")">
                            <a class="page-link text-black" asp-route-pageNumber="1"
                               asp-route-sortOrder="@ViewData["CurrentSort"]"
                               asp-route-currentFilter="@ViewData["CurrentFilter"]">
                                <span>First</span>
                            </a>
                        </li>

                        <li class="page-item @(Model.HasPreviousPage ? "" : "disabled")">
                            <a class="page-link text-black" asp-route-pageNumber="@(Model.PageIndex - 1)"
                               asp-route-sortOrder="@ViewData["CurrentSort"]"
                               asp-route-currentFilter="@ViewData["CurrentFilter"]">
                                <span>Previous</span>
                            </a>
                        </li>

                        @for (int i = 1; i <= Model.TotalPages; i++)
                        {
                            <li class="page-item @(Model.PageIndex == i ? "active" : "")">
                                <a class="page-link text-black" asp-route-pageNumber="@i"
                                   asp-route-sortOrder="@ViewData["CurrentSort"]"
                                   asp-route-currentFilter="@ViewData["CurrentFilter"]">@i</a>
                            </li>
                        }

                        <li class="page-item @(Model.HasNextPage ? "" : "disabled")">
                            <a class="page-link text-black" asp-route-pageNumber="@(Model.PageIndex + 1)"
                               asp-route-sortOrder="@ViewData["CurrentSort"]"
                               asp-route-currentFilter="@ViewData["CurrentFilter"]">
                                <span>Next</span>
                            </a>
                        </li>
                        <li class="page-item @(Model.PageIndex == Model.TotalPages ? "disabled" : "")">
                            <a class="page-link text-black" asp-route-pageNumber="@Model.TotalPages"
                               asp-route-sortOrder="@ViewData["CurrentSort"]"
                               asp-route-currentFilter="@ViewData["CurrentFilter"]">
                                <span>Last</span>
                            </a>
                        </li>


                    </ul>
                </nav>
            </div>
        </div>

    </tbody>
</table>
