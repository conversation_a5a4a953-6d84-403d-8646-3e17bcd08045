﻿@model Android_OTA.Models.connection_settings
@using Microsoft.AspNetCore.Http


@{
    ViewData["Title"] = "Create";
}

<h1>Create</h1>

<h4>User</h4>
<hr />
<div class="row">
    <div class="col-md-4">
        <form asp-action="Create">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <div class="form-group">
                <label asp-for="setting_name" class="control-label"></label>
                <input asp-for="setting_name" class="form-control" />
                <span asp-validation-for="setting_name" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="setting_value" class="control-label"></label>
                <input asp-for="setting_value" class="form-control" />
                <span asp-validation-for="setting_value" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="description" class="control-label"></label>
                <input asp-for="description" class="form-control" />
                <span asp-validation-for="description" class="text-danger"></span>
            </div>

@*            <div class="form-group">
                <label asp-for="CreateDate" class="control-label"></label>
                <input asp-for="CreateDate" class="form-control" />
                <span asp-validation-for="CreateDate" class="text-danger"></span>
            </div>
*@            <div class="form-group">
                <input type="submit" value="Create" class="btn btn-primary" />
            </div>
        </form>
    </div>
</div>

<div>
    <a asp-action="Index">Back to List</a>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
