﻿@model Android_OTA.Models.connection_settings
@using Microsoft.AspNetCore.Http

@{
    ViewData["Title"] = "Details";
}

<h1>Details</h1>

<div>
    <h4>User</h4>
    <hr />
    <dl class="row">
        <dt class="col-sm-2">
            @Html.DisplayNameFor(model => model.setting_name)
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.setting_name)
        </dd>
        <dt class="col-sm-2">
            @Html.DisplayNameFor(model => model.setting_value)
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.setting_value)
        </dd>
        <dt class="col-sm-2">
            @Html.DisplayNameFor(model => model.description)
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.description)
        </dd>
    </dl>
</div>
<div>
    <a asp-action="Edit" asp-route-id="@Model.idx">Edit</a> |
    <a asp-action="Index">Back to List</a>
</div>
