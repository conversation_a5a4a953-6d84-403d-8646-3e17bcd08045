﻿@model Android_OTA.Models.connection_settings
@using Microsoft.AspNetCore.Http


@{
    ViewData["Title"] = "Delete";
}

<h1>Delete</h1>

<h3>Are you sure you want to delete this?</h3>
<div>
    <h4>User</h4>
    <hr />
    <dl class="row">
        <dt class="col-sm-2">
            @Html.DisplayNameFor(model => model.setting_name)
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.setting_name)
        </dd>
        <dt class="col-sm-2">
            @Html.DisplayNameFor(model => model.setting_value)
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.setting_value)
        </dd>
        <dt class="col-sm-2">
            @Html.DisplayNameFor(model => model.description)
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.description)
        </dd>
    </dl>

    <form asp-action="Delete">
        <input type="hidden" asp-for="idx" />
        <input type="submit" value="Delete" class="btn btn-danger" /> |
        <a asp-action="Index">Back to List</a>
    </form>
</div>
