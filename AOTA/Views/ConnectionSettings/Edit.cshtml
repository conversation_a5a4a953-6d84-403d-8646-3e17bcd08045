﻿@model Android_OTA.Models.connection_settings
@using Microsoft.AspNetCore.Http
@{
    ViewData["Title"] = "Edit";
}

<h1>Edit</h1>

<h4>ConnectionSettings</h4>
<hr />
<div class="row">
    <div class="col-md-4">
        <form asp-action="Edit">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <input type="hidden" asp-for="idx" />
@*             <div class="form-group">
                <label asp-for="idx" class="control-label"></label>
                <input asp-for="idx" class="form-control" />
                <span asp-validation-for="idx" class="text-danger"></span>
            </div> *@
            <div class="form-group">
                <label asp-for="setting_name" class="control-label"></label>
                <input asp-for="setting_name" class="form-control" />
                <span asp-validation-for="setting_name" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="setting_value" class="control-label"></label>
                <input asp-for="setting_value" class="form-control" />
                <span asp-validation-for="setting_value" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="description" class="control-label"></label>
                <input asp-for="description" class="form-control" />
                <span asp-validation-for="description" class="text-danger"></span>
            </div>
            <div class="form-group">
                <input type="submit" value="Save" class="btn btn-primary" />
            </div>
        </form>
    </div>
</div>

<div>
    <a asp-action="Index">Back to List</a>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
