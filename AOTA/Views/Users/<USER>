﻿@model Android_OTA.Models.user
@using Microsoft.AspNetCore.Http


@{
    ViewData["Title"] = "Delete";
}

<h1>Delete</h1>

<h3>Are you sure you want to delete this?</h3>
<div>
    <h4>User</h4>
    <hr />
    <dl class="row">
        <dt class="col-sm-2">
            @Html.DisplayNameFor(model => model.user_name)
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.user_name)
        </dd>
        <dt class="col-sm-2">
            @Html.DisplayNameFor(model => model.password)
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.password)
        </dd>
        <dt class="col-sm-2">
            @Html.DisplayNameFor(model => model.create_date)
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.create_date)
        </dd>
    </dl>

    <form asp-action="Delete">
        <input type="hidden" asp-for="user_id" />
        <input type="submit" value="Delete" class="btn btn-danger" /> |
        <a asp-action="Index">Back to List</a>
    </form>
</div>
