﻿@model Android_OTA.Models.user
@using Microsoft.AspNetCore.Http

@{
    ViewData["Title"] = "Details";
}

<h1>Details</h1>

<div>
    <h4>User</h4>
    <hr />
    <dl class="row">
        <dt class="col-sm-2">
            @Html.DisplayNameFor(model => model.user_id)
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.user_id)
        </dd>
        <dt class="col-sm-2">
            @Html.DisplayNameFor(model => model.user_name)
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.user_name)
        </dd>
        <dt class="col-sm-2">
            @Html.DisplayNameFor(model => model.password)
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.password)
        </dd>
        <dt class="col-sm-2">
            @Html.DisplayNameFor(model => model.create_date)
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.create_date)
        </dd>
    </dl>
</div>
<div>
    <a asp-action="Edit" asp-route-id="@Model.user_id">Edit</a> |
    <a asp-action="Index">Back to List</a>
</div>
