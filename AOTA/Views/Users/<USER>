﻿@model IEnumerable<Android_OTA.Models.user>
@using Microsoft.AspNetCore.Http

@{
    ViewData["Title"] = "Index";
}

<h1>User</h1>

<p>
    <a asp-action="Create">Create New</a>
</p>
<table class="table">
    <thead>
        <tr>
            <th>
                @Html.DisplayNameFor(model => model.user_id)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.user_name)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.password)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.type)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.create_date)
            </th>
            <th></th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model)
        {
            <tr>
                <td>
                    @Html.DisplayFor(modelItem => item.user_id)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.user_name)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.password)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.type)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.create_date)
                </td>
                <td>
                    <a asp-action="Edit" asp-route-id="@item.user_id">Edit</a> |
                    <a asp-action="Details" asp-route-id="@item.user_id">Details</a> |
                    <a asp-action="Delete" asp-route-id="@item.user_id">Delete</a>
                </td>
            </tr>
        }
    </tbody>
</table>
