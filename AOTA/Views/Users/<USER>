﻿@model Android_OTA.Models.user
@using Microsoft.AspNetCore.Http
@{
    ViewData["Title"] = "Edit";
}

<h1>Edit</h1>

<h4>User</h4>
<hr />
<div class="row">
    <div class="col-md-4">
        <form asp-action="Edit">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <input type="hidden" asp-for="user_id" />
            <div class="form-group">
                <label asp-for="user_name" class="control-label"></label>
                <input asp-for="user_name" class="form-control" />
                <span asp-validation-for="user_name" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="password" class="control-label"></label>
                <input asp-for="password" class="form-control" />
                <span asp-validation-for="password" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="type" class="control-label"></label>
                <input asp-for="type" class="form-control" />
                <span asp-validation-for="type" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="create_date" class="control-label"></label>
                <input asp-for="create_date" class="form-control" />
                <span asp-validation-for="create_date" class="text-danger"></span>
            </div>
            <div class="form-group">
                <input type="submit" value="Save" class="btn btn-primary" />
            </div>
        </form>
    </div>
</div>

<div>
    <a asp-action="Index">Back to List</a>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
