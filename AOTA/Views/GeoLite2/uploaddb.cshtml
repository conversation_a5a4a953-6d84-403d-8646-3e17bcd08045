﻿@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@{
    ViewData["Title"] = "Upload GeoLite2 DB";
}

<div class="p-4 container">
    <div class="row p-4">
        <div class="col-md-12">
            <h3>Upload GeoLite2 DB</h3>
        </div>
        <div class="mb-3">
            <span class="text-danger">@ViewData["ErrorMessage"]</span>
            <span class="text-success">@ViewData["Message"]</span>
        </div>
    </div>
    <div class="row p-4">
            <form method="post" enctype="multipart/form-data" class="d-flex" asp-action="UploadDb" asp-controller="GeoLite2">
                <div class="">
                    <input name="file" class="form-control me-1" type="file" aria-label="GeoLiteDb">
                </div>
                <div class="">
                    <button class="btn btn-primary bi bi-cloud-arrow-up-fill" type="submit">Upload GeoLite2DB</button>
                </div>
            </form>
    </div>
</div>
