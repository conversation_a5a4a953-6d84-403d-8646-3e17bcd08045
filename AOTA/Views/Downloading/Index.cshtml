﻿@model IEnumerable<Android_OTA.Models.current_connections>
@using Microsoft.AspNetCore.Http

@{
    ViewData["Title"] = "Index";
}

<h1>Current Connections</h1>
@* 
<p>
    <a asp-action="Create">Create New</a>
</p>

 *@
<table class="table">
    <thead>
        <tr>
@*              <th>
                @Html.DisplayNameFor(model => model.idx)
            </th> *@
            <th>
                @Html.DisplayNameFor(model => model.model_name)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.device_id)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.start_date)
            </th>
            <th></th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model)
        {
            //<tr data-id ="@item.idx">
            <tr>
@*                 <td>
                    @Html.DisplayFor(modelItem => item.idx)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.setting_name)
                </td> *@
                <td>
                    @Html.DisplayFor(modelItem => item.model_name)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.device_id)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.start_date)
                </td>
                <td>
@*                     <a asp-action="Edit" asp-route-idx="@item.idx">Edit</a> |
                    <a asp-action="Details" asp-route-idx="@item.idx">Details</a> |
                    <a asp-action="Delete" asp-route-idx="@item.idx">Delete</a>
 *@                 <a asp-action="Delete" asp-route-idx="@item.idx">Delete</a>
                </td>
            </tr>
        }
    </tbody>
</table>
