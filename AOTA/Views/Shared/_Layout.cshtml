﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - Android OTA</title>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/bootstrap.css" asp-append-version="true" />
</head>
<body>
     <div class="container-fluid"> 
        <div class="row flex-nowrap">         
            @* <div class="col-12 col-md-3 col-xl-2 px-sm-2 px-0 bg-dark"> *@
            <div class="col-2 col-md-2 col-xl-2 px-sm-2 px-0 bg-dark">
                <div class="d-flex flex-column align-items-center align-items-sm-start px-3 pt-2 text-white min-vh-100">
                    <a href="/" class="d-flex align-items-center pb-3 mb-md-0 me-md-auto text-white text-decoration-none">
                        <span class="fs-5 d-none d-sm-inline">Android OTA</span>
                    </a>
                    <ul class="nav nav-pills flex-column mb-sm-auto mb-0 align-items-center align-items-sm-start" id="menu">
                        <li class="nav-item">
                            <a asp-controller="Home" asp-action="Index" class="nav-link align-middle px-0 text-white">
                                <i class="fs-4 bi bi-house-fill"></i><span class="ms-1 d-none d-sm-inline">Home</span>
                            </a>
                        </li>
                         <li class="nav-item">
                            <a asp-controller="Downloading" asp-action="Index" class="nav-link align-middle px-0 text-white">
                                <i class="fs-4 bi bi-cloud-download"></i><span class="ms-1 d-none d-sm-inline">Downloading</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a asp-controller="Completed" asp-action="Index" class="nav-link align-middle px-0 text-white">
                                <i class="fs-4 bi bi-cloud-check"></i><span class="ms-1 d-none d-sm-inline">Completed</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a asp-controller="Log" asp-action="Index" class="nav-link align-middle px-0 text-white">
                                <i class="fs-4 bi bi-file-earmark-arrow-down"></i><span class="ms-1 d-none d-sm-inline">Log</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a asp-controller="Users" asp-action="Index" class="nav-link align-middle px-0 text-white">
                                <i class="fs-4 bi bi-person-fill"></i><span class="ms-1 d-none d-sm-inline">User</span>
                            </a>
                        </li>

@*                         <li class="nav-item">
                            <a asp-controller="AgentRepository" asp-action="Index" class="nav-link align-middle px-0 text-white">
                                <i class="fs-4 bi bi-archive-fill"></i><span class="ms-1 d-none d-sm-inline">Repository</span>
                            </a>
                        </li>
 *@
                        <li class="nav-item">
                            <a asp-controller="ConnectionSettings" asp-action="Index" class="nav-link align-middle px-0 text-white">
                                <i class="fs-4 bi bi-gear"></i><span class="ms-1 d-none d-sm-inline">ConnectionSettings</span>
                            </a>
                        </li>


                        <li class="nav-item">
                            <a asp-controller="Logout" asp-action="Index" class="nav-link align-middle px-0 text-white">
                                <i class="fs-4 bi bi-box-arrow-left"></i><span class="ms-1 d-none d-sm-inline">Logout</span>
                            </a>
                        </li>

@* 
                        <li>
                            <a href="#submenu1" data-bs-toggle="collapse" class="nav-link px-0 align-middle text-white">
                                <i class="fs-4 bi bi-pc-horizontal"></i><span class="ms-1 d-none d-sm-inline">Devices</span>
                            </a>
                            <ul class="collapse show nav flex-column ms-1" id="submenu1" data-bs-parent="#menu">
                                <li class="w-100">
                                    <a asp-controller="Devices" asp-action="Index" class="nav-link px-0 text-white"> List all</a>
                                </li>
                                <li>
                                    <a asp-controller="Device" asp-action="Add" class="nav-link px-0 text-white"> Add employee </a>
                                </li>
                            </ul>
                        </li>

                         <li class="nav-item">
                            <a asp-controller="Devices" asp-action="Index" class="nav-link align-middle px-0 text-white">
                                <i class="fs-4 bi bi-pc-horizontal"></i><span class="ms-1 d-none d-sm-inline">Devices</span>
                            </a>
                        </li>
                         
                        <li>
                            <a href="#submenu2" data-bs-toggle="collapse" class="nav-link px-0 align-middle text-white">
                                <i class="fs-4 bi bi-person-fill"></i><span class="ms-1 d-none d-sm-inline">Users</span>
                            </a>
                            <ul class="collapse show nav flex-column ms-1" id="submenu2" data-bs-parent="#menu">
                                <li class="w-100">
                                    <a asp-controller="Users" asp-action="Index" class="nav-link px-0 text-white"> List all</a>
                                </li>
                                <li>
                                    <a asp-controller="Users" asp-action="Create" class="nav-link px-0 text-white"> Add User </a>
                                </li>
                            </ul>
                        </li>

                        <li>
                            <a href="#submenu3" data-bs-toggle="collapse" class="collapsed nav-link px-0 align-middle text-white">
                                <i class="fs-4 bi bi-archive-fill"></i><span class="ms-1 d-none d-sm-inline">Repository</span>
                            </a>
                            <ul class="collapse show nav flex-column ms-1" id="submenu3" data-bs-parent="#menu">
                                <li class="w-100">
                                    <a asp-controller="AgentRepository" asp-action="Index" class="nav-link px-0 text-white"> List all</a>
                                </li>
                                <li>
                                    <a asp-controller="AgentRepository" asp-action="Create" class="nav-link px-0 text-white"> Add Repository </a>
                                </li>
                            </ul>
                        </li> *@
                    </ul>
                    <hr>

                </div>
            </div>
            <div class="col py-3">
                @RenderBody()
            </div>
        </div>
    </div>

@*     <footer class="border-top footer text-muted">
        <div class="container">
            &copy; Flytech - Big Data Collection<a asp-area="" asp-controller="Home" asp-action="Privacy">Privacy</a>
        </div>
    </footer> *@
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>

    <script src="~/lib/jquery/dist/jquery.js"></script>
    <script src="~/lib/jquery-validation/dist/jquery.validate.js"></script>
    <script src="~/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js"></script>

    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
