﻿@if (User.Identity.IsAuthenticated)
{
<form asp-controller="Logout" asp-action="" method="post" class="navbar-right">
    <div>        
        <ul class="nav navbar-nav navbar-right">

            <li>
                <a href="#">@User.Identity.Name</a>
            </li>
            <li>
                <button type="submit" class="btn btn-link navbar-btn navbar-link">
                    Logout
                </button>
            </li>
        </ul>
    </div>
</form> 
}
else            
{
<ul class="nav navbar-nav navbar-right">
    <li>
        <a asp-controller="Login" asp-action="" asp-route-returnUrl="/Home">
            Login
        </a>
    </li>
</ul>
}
