﻿@* @model IEnumerable<Android_OTA.Models.completed_connections> *@
@model PaginatedList<Android_OTA.Models.completed_connections>
@using Microsoft.AspNetCore.Http

@{
    ViewData["Title"] = "Index";
    var currentFilter = ViewBag.CurrentFilter as string ?? "";
    var currentSort = ViewBag.CurrentSort as string ?? "";
}

<h1>Completed Connections</h1>
@* 
<p>
    <a asp-action="Create">Create New</a>
</p>
 *@

<form asp-action="Index" method="get" class="row gy-2 gx-3 align-items-center mb-3">
    <div class="col-auto">
    </div>
    <div class="col-auto">
        <label for="searchString" class="col-form-label">Device ID:</label>
    </div>
    <div class="col-auto">
        <input type="text"
               name="searchString"
               id="searchString"
               value="@ViewBag.CurrentFilter"
               class="form-control"
               placeholder="Search by Device ID" />
    </div>
    <div class="col-auto">
        <button type="submit" class="btn btn-primary">Search</button>
    </div>
</form>

<table class="table">
    <thead>
        <tr>
            <th scope="col">model_name</th>
            <th>
                @Html.ActionLink(
                         "Device ID",
                         "Index",
                         new { sortOrder = ViewBag.DeviceSortParm, searchString = currentFilter }
                         )
            </th>
            <th scope="col">start_date</th>
            <th>
                @Html.ActionLink(
                         "finish_date",
                         "Index",
                         new { sortOrder = ViewBag.DateSortParm, searchString = currentFilter }
                         )
            </th>
            <th>
                @Html.ActionLink(
                         "elapsed_time",
                         "Index",
                         new { sortOrder = ViewBag.ElapsedSortParm, searchString = currentFilter }
                         )
            </th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model.Items)
        {
            //<tr data-id ="@item.idx">
            <tr>
@*                 <td>
                    @Html.DisplayFor(modelItem => item.idx)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.setting_name)
                </td> *@
                <td>
                    @Html.DisplayFor(modelItem => item.model_name)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.device_id)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.start_date)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.finish_date)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.elapsed_time)
                </td>
                <td>
@*                     <a asp-action="Edit" asp-route-idx="@item.idx">Edit</a> |
                    <a asp-action="Details" asp-route-idx="@item.idx">Details</a> |
                    <a asp-action="Delete" asp-route-idx="@item.idx">Delete</a>
 *@                 <a asp-action="Delete" asp-route-idx="@item.idx">Delete</a>
                </td>
            </tr>
        }
        @*Paging*@
        <div class="row">
            <div class="col-md-6">
            </div>
            <div class="col-md-6">
                <nav>
                    <ul class="pagination justify-content-end">
                        <li class="page-item @(Model.PageIndex == 1 ? "disabled" : "")">
                            <a class="page-link text-black" asp-route-pageNumber="1"
                               asp-route-sortOrder="@ViewData["CurrentSort"]"
                               asp-route-currentFilter="@ViewData["CurrentFilter"]">
                                <span>First</span>
                            </a>
                        </li>

                        <li class="page-item @(Model.HasPreviousPage ? "" : "disabled")">
                            <a class="page-link text-black" asp-route-pageNumber="@(Model.PageIndex - 1)"
                               asp-route-sortOrder="@ViewData["CurrentSort"]"
                               asp-route-currentFilter="@ViewData["CurrentFilter"]">
                                <span>Previous</span>
                            </a>
                        </li>

                        @for (int i = 1; i <= Model.TotalPages; i++)
                        {
                                <li class="page-item @(Model.PageIndex == i ? "active" : "")">
                                    <a class="page-link text-black" asp-route-pageNumber="@i"
                                           asp-route-sortOrder="@ViewData["CurrentSort"]"
                                       asp-route-currentFilter="@ViewData["CurrentFilter"]">@i</a>
                                </li>
                        }

                        <li class="page-item @(Model.HasNextPage ? "" : "disabled")">
                            <a class="page-link text-black" asp-route-pageNumber="@(Model.PageIndex + 1)"
                               asp-route-sortOrder="@ViewData["CurrentSort"]"
                               asp-route-currentFilter="@ViewData["CurrentFilter"]">
                                <span>Next</span>
                            </a>
                        </li>
                        <li class="page-item @(Model.PageIndex == Model.TotalPages ? "disabled" : "")">
                            <a class="page-link text-black" asp-route-pageNumber="@Model.TotalPages"
                               asp-route-sortOrder="@ViewData["CurrentSort"]"
                               asp-route-currentFilter="@ViewData["CurrentFilter"]">
                                <span>Last</span>
                            </a>
                        </li>


                    </ul>
                </nav>
            </div>
        </div>

    </tbody>
</table>
