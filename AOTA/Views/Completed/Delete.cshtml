﻿@model Android_OTA.Models.completed_connections
@using Microsoft.AspNetCore.Http


@{
    ViewData["Title"] = "Delete";
}

<h1>Delete</h1>

<h3>Are you sure you want to delete this?</h3>
<div>
    <h4>User</h4>
    <hr />
    <dl class="row">
        <dt class="col-sm-2">
            @Html.DisplayNameFor(model => model.model_name)
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.model_name)
        </dd>
        <dt class="col-sm-2">
            @Html.DisplayNameFor(model => model.device_id)
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.device_id)
        </dd>
        <dt class="col-sm-2">
            @Html.DisplayNameFor(model => model.start_date)
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.start_date)
        </dd>
        <dt class="col-sm-2">
            @Html.DisplayNameFor(model => model.finish_date)
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.finish_date)
        </dd>
        <dt class="col-sm-2">
            @Html.DisplayNameFor(model => model.elapsed_time)
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.elapsed_time)
        </dd>

    </dl>

    <form asp-action="Delete">
        <input type="hidden" asp-for="idx" />
        <input type="submit" value="Delete" class="btn btn-danger" /> |
        <a asp-action="Index">Back to List</a>
    </form>
</div>
