﻿@{
    Layout = null;
}
@model Android_OTA.Models.user
<!DOCTYPE html>
<html>

<head>
    <title>Login</title>

    <meta charset="UTF-8">
    <meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
    <title></title>
    <!-- Favicon-->
    <link rel="icon" href="~/favicon.ico" type="image/x-icon">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css?family=Roboto:400,700&subset=latin,cyrillic-ext" rel="stylesheet" type="text/css">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet" type="text/css">

    <!-- Bootstrap Core Css -->
    <link href="~/css/bootstrap.css" rel="stylesheet">

    <!-- Waves Effect Css -->
    <link href="~/plugins/node-waves/waves.css" rel="stylesheet" />

    <!-- Animation Css -->
    <link href="~/plugins/animate-css/animate.css" rel="stylesheet" />

    <!-- Custom Css -->
    <link href="~/css/style.css" rel="stylesheet">
</head>

<body class="login-page">
    <div class="login-box">
        <div class="logo">
            <a href="javascript:void(0);"><b>Flytech</b></a>
            <small>Android OTA</small>
        </div>
        <div class="card">
            <div class="body">
                <form id="sign_in" method="POST">
                    @{
                        if (ViewBag.LoginStatus != null)
                        {
                            if (ViewBag.LoginStatus == 0)
                            {
                                <div class="errmsg">Invalid login detail</div>
                            }
                            else if (ViewBag.LoginStatus == -1)
                            {
                                <div class="errmsg">The account has been locked for 15 minutes</div>
                            }

                        }
                        }
                    <div class="input-group">
                        <span class="input-group-addon">
                            <i class="material-icons">person</i>
                        </span>
                        <div class="form-line">
                            <input type="user id" class="form-control" asp-for="user_id" placeholder="User ID" required>
                        </div>
                    </div>
                    <div class="input-group">
                        <span class="input-group-addon">
                            <i class="material-icons">lock</i>
                        </span>
                        <div class="form-line">
                            <input type="password" class="form-control" asp-for="password" placeholder="Password" required>
                        </div>
                    </div>
                     <div class="row">
@*                          <div class="col-xs-8 p-t-5">
                            <input type="checkbox" asp-for="type" id="rememberme" class="filled-in chk-col-pink">
                            <label for="rememberme">Remember Me</label>
                        </div>
 *@                         <div class="col-xs-4">
                            <button class="btn btn-block bg-pink waves-effect" type="submit">SIGN IN</button>
                        </div>
                    </div>
                     
@*                     <div class="row m-t-15 m-b--20">
                        <div class="col-xs-6">
                            <a asp-controller="Account" asp-action="Register">Register Now!</a>
                        </div>
                        <div class="col-xs-6 align-right">
                            <a asp-controller="Account" asp-action="ForgetPassword">Forgot Password?</a>
                        </div>
                    </div>
 *@                </form>
            </div>
        </div>
    </div>

    <!-- Jquery Core Js --><!-- Validation Plugin Js -->
    <script src="~/plugins/jquery/jquery.min.js"></script>
    <script src="~/plugins/jquery-validation/jquery.validate.js"></script>
    <script src="~/plugins/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js"></script>
    <!-- Bootstrap Core Js -->
    <script src="~/plugins/bootstrap/js/bootstrap.js"></script>

    <!-- Waves Effect Plugin Js -->
    <script src="~/plugins/node-waves/waves.js"></script>



    <!-- Custom Js -->
    <script src="~/js/site.js"></script>
    <script src="~/js/pages/examples/sign-in.js"></script>
</body>

</html>