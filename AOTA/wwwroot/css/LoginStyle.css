﻿/* Made with love by <PERSON><PERSON><PERSON><PERSON>*/

@import url('https://fonts.googleapis.com/css?family=Numans');

html, body {
    /*    https://getwallpapers.com/wallpaper/full/3/a/0/51778.jpg
    https://getwallpapers.com/wallpaper/full/2/2/e/233505.jpg
*/ background-image: url('https://getwallpapers.com/wallpaper/full/f/c/4/889627-vertical-color-pink-wallpaper-2880x1800-laptop.jpg');
    height: 100%;
    font-family: 'Numans', sans-serif;
}

.container {
    height: 100%;
    align-content: center;
}

.card {
    height: 370px;
    margin-top: auto;
    margin-bottom: auto;
    width: 400px;
    background-color: rgba(0,0,0,0.5) !important;
}

.social_icon span {
    font-size: 60px;
    margin-left: 10px;
    color: #FFC312;
}

    .social_icon span:hover {
        color: white;
        cursor: pointer;
    }

.card-header h3 {
    color: white;
}

.social_icon {
    position: absolute;
    right: 20px;
    top: -45px;
}

.input-group-prepend span {
    width: 50px;
    background-color: #3596DD;
    color: black;
    border: 0 !important;
}

input:focus {
    outline: 0 0 0 0 !important;
    box-shadow: 0 0 0 0 !important;
}

.remember {
    color: white;
}

    .remember input {
        width: 20px;
        height: 20px;
        margin-left: 15px;
        margin-right: 5px;
    }

.login_btn {
    color: black;
    background-color: #3596DD;
    width: 100px;
}

    .login_btn:hover {
        color: black;
        background-color: white;
    }

.links {
    color: white;
}

    .links a {
        margin-left: 4px;
    }
     