﻿using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;

namespace Android_OTA.Models
{
    public partial class otaDbContext : DbContext
    {
        public otaDbContext()
        {
        }

        public otaDbContext(DbContextOptions<otaDbContext> options)
            : base(options)
        {
        }

        public virtual DbSet<login_failed> LoginFailedSet { get; set; }
        public virtual DbSet<user> Users { get; set; }
        public virtual DbSet<connection_settings> ConnectionSettings { get; set; }
        public virtual DbSet<current_connections> CurrentConnections { get; set; }
        public virtual DbSet<completed_connections> CompletedConnections { get; set; }
        public virtual DbSet<log> Logs { get; set; }


        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.UseCollation("utf8mb3_general_ci")
                .HasCharSet("utf8mb3");

            modelBuilder.Entity<current_connections>(entity =>
            {
                entity.ToTable("current_connections");
                entity.HasKey(e => e.idx).HasName("PRIMARY");
                entity.Property(e => e.idx).HasColumnType("int");
                entity.Property(e => e.model_name).HasMaxLength(45);
                entity.Property(e => e.device_id).HasMaxLength(45);
                entity.Property(e => e.start_date).HasColumnType("datetime");
                entity.Property(e => e.finish_date).HasColumnType("datetime");
                entity.Property(e => e.elapsed_time).HasColumnType("int");
            });

            modelBuilder.Entity<completed_connections>(entity =>
            {
                entity.ToTable("completed_connections");
                entity.HasKey(e => e.idx).HasName("PRIMARY");
                entity.Property(e => e.idx).HasColumnType("int");
                entity.Property(e => e.model_name).HasMaxLength(45);
                entity.Property(e => e.device_id).HasMaxLength(45);
                entity.Property(e => e.start_date).HasColumnType("datetime");
                entity.Property(e => e.finish_date).HasColumnType("datetime");
                entity.Property(e => e.elapsed_time).HasColumnType("int");
            });

            modelBuilder.Entity<connection_settings>(entity =>
            {
                entity.ToTable("connection_settings");
                entity.HasKey(e => e.idx).HasName("PRIMARY");
                entity.Property(e => e.idx).HasColumnType("int");
                entity.Property(e => e.setting_name).HasMaxLength(45);
                entity.Property(e => e.setting_value).HasMaxLength(45);
                entity.Property(e => e.description).HasMaxLength(45);
            });

            modelBuilder.Entity<log>(entity =>
            {
                entity.ToTable("log");
                entity.HasKey(e => e.idx).HasName("PRIMARY");
                entity.Property(e => e.idx).HasColumnType("int");
                entity.Property(e => e.device_id).HasMaxLength(45);
                entity.Property(e => e.download_status).HasMaxLength(45);
                entity.Property(e => e.date_time).HasColumnType("datetime");
                entity.Property(e => e.ip).HasMaxLength(15);
                entity.Property(e => e.status).HasMaxLength(100);
            });

            modelBuilder.Entity<login_failed>(entity =>
            {
                entity.ToTable("login_failed");
                entity.HasKey(e => e.user_id)
                    .HasName("PRIMARY");
                entity.Property(e => e.user_id).HasMaxLength(64);
                entity.Property(e => e.failed_time).HasColumnType("datetime");
            });

            modelBuilder.Entity<user>(entity =>
            {
                entity.ToTable("user");
                entity.HasKey(e => e.user_id)
                    .HasName("PRIMARY");
                entity.Property(e => e.user_id).HasMaxLength(64);
                entity.Property(e => e.create_date).HasColumnType("datetime");
                entity.Property(e => e.password).HasMaxLength(50);
                entity.Property(e => e.type).HasMaxLength(50);
                entity.Property(e => e.user_name).HasMaxLength(128);
            });

            OnModelCreatingPartial(modelBuilder);
        }

        partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
    }
}
